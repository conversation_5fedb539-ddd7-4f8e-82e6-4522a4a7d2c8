const { withXcodeProject, withGradleProperties } = require('expo/config-plugins');

module.exports = function withDisableBitcode(config, { android }) {
  // Configuração para iOS
  config = withXcodeProject(config, async (config) => {
    const xcodeProject = config.modResults;
    
    // Desativa Bitcode para todos os targets e configurações
    Object.entries(xcodeProject.pbxXCBuildConfigurationSection()).forEach(
      ([, buildConfig]) => {
        if (buildConfig.buildSettings) {
          buildConfig.buildSettings.ENABLE_BITCODE = 'NO';
        }
      }
    );

    return config;
  });

  // Configuração para Android
  if (android) {
    config = withGradleProperties(config, (config) => {
      const gradleProperties = config.modResults;
      
      // Adiciona configurações específicas do Android
      gradleProperties.push({
        type: 'property',
        key: 'android.compileSdkVersion',
        value: android.compileSdkVersion?.toString() || '34'
      });
      
      gradleProperties.push({
        type: 'property',
        key: 'android.targetSdkVersion',
        value: android.targetSdkVersion?.toString() || '34'
      });
      
      gradleProperties.push({
        type: 'property',
        key: 'android.buildToolsVersion',
        value: android.buildToolsVersion || '34.0.0'
      });
      
      return config;
    });
  }

  return config;
};
