{"name": "snapfit", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"expo": "~48.0.21", "expo-av": "~13.2.1", "expo-build-properties": "~0.6.0", "expo-camera": "~13.2.1", "expo-document-picker": "~11.2.2", "expo-image-picker": "~14.1.1", "expo-media-library": "~15.2.3", "expo-modules-core": "~1.2.7", "expo-splash-screen": "~0.18.2", "expo-status-bar": "~1.4.4", "react": "18.2.0", "react-native": "0.71.14", "react-native-iap": "^12.10.7", "react-native-webview": "11.26.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@expo/config-plugins": "~6.0.0", "@react-native-community/cli": "10.2.7", "@react-native-community/cli-platform-android": "10.2.0"}, "private": true}