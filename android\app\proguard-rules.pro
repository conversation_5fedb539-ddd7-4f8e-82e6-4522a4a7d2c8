-keep class com.facebook.react.uimanager.ViewGroupManager { *; }
-keep class com.facebook.react.uimanager.ViewManager { *; }
-keep class com.facebook.react.views.art.** { *; }
-keep class com.facebook.react.views.checkbox.** { *; }
-keep class com.facebook.react.views.picker.** { *; }
-keep class com.facebook.react.views.progressbar.** { *; }
-keep class com.facebook.react.views.scroll.** { *; }
-keep class com.facebook.react.views.slider.** { *; }
-keep class com.facebook.react.views.swiperefresh.** { *; }
-keep class com.facebook.react.views.switch.** { *; }
-keep class com.facebook.react.views.text.** { *; }
-keep class com.facebook.react.views.textinput.** { *; }
-keep class com.facebook.react.views.toolbar.** { *; }
-keep class com.facebook.react.views.webview.** { *; }
-keep public class com.android.vending.billing.IInAppBillingService {
    <methods>;
}
-keep public class com.android.vending.billing.IInAppBillingService$Stub {
    <methods>;
}
-keep public class com.android.vending.billing.IInAppBillingService$Stub$Proxy {
    <methods>;
}
-dontwarn com.android.vending.billing.**
-keep class com.facebook.soloader.** {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.** {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.keychain.SharedPrefsBackedKeyChain {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.cipher.NativeGCMCipher {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.mac.NativeMac {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.random.NativeRandom {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibrary {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryNoop {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV2 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV3 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV4 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV5 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV6 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV7 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV8 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV9 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV10 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV11 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV12 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV13 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV14 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV15 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV16 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV17 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV18 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV19 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV20 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV21 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV22 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV23 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV24 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV25 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV26 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV27 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV28 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV29 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV30 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV31 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV32 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV33 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV34 {
    <fields>;
    <methods>;
}
-keep class com.facebook.crypto.util.SystemNativeCryptoLibraryV35 {
    <fields>;
    <method