{"cli": {"version": ">= 3.9.1"}, "build": {"release": {"ios": {"image": "latest", "distribution": "store", "buildConfiguration": "Release"}}, "development": {"developmentClient": true, "distribution": "internal", "ios": {"image": "latest", "resourceClass": "medium"}}, "preview": {"distribution": "internal", "ios": {"image": "latest", "resourceClass": "medium"}}, "production": {"android": {"image": "latest", "resourceClass": "medium", "gradleCommand": ":app:bundleRelease", "buildType": "apk", "distribution": "store", "env": {"EXPO_NO_CAPABILITY_SYNC": "1"}}, "ios": {"image": "latest", "resourceClass": "medium", "distribution": "store"}}}, "submit": {"production": {}}}